# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
node_modules/
.pnp
.pnp.js

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# parcel
.parcel-cache/
dist/
dist.zip

# clasp
.clasp.json

# Optional npm cache directory
.npm 

# env
.env

# gcloud CLI files
google-cloud-sdk/
google-cloud-*.tar.gz
.google_application_credentials.json
**/.google_application_credentials.json
**/appscript-296515-firebase-adminsdk-e9ksw-539ce686c1.json
bin/
lib/
deno_cache/

# csv files
**/*.csv

# lockfile
deno.lock

# audit
.audit/