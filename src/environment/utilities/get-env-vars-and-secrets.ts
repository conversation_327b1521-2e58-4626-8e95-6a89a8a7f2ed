import { Impersonator } from "modules/cloud-run-impersonator/index.ts";
import { ProcessSchema } from "types/process-schema.ts";
import { getProcessEnvVars } from "../build-scripts/utilities/get-process-env-vars.ts";
import { Logger } from "modules/logger/index.ts";

export const getEnvVarsAndSecrets = async (options: {
  processFolderAbsolutePath: string;
  stageName: ProcessSchema.StageName;
}): Promise<Record<string, string>> => {
  const { processFolderAbsolutePath, stageName } = options;
  const env: Record<string, string> = {};
  const packageJSONContent = await Deno.readTextFile(
    `${processFolderAbsolutePath}/package.json`,
  );
  const packageJSON = JSON.parse(packageJSONContent) as ProcessSchema.CloudRunDenoProcess;
  const processEnvVars = getProcessEnvVars(packageJSON as ProcessSchema.CloudRunDenoProcess);
  const envVars = processEnvVars.envVars
    .filter((envVar) => envVar.value[stageName] !== null);
  for (const envVar of envVars) {
    env[envVar.key] = envVar.value[stageName] as string;
  }
  const impersonator = new Impersonator({
    scopes: [
      "https://www.googleapis.com/auth/cloud-platform",
    ],
    subject: "<EMAIL>",
  });
  const secrets = processEnvVars.secrets
    .filter((secret) => secret.secretName[stageName] !== null);
  const secretManager = await impersonator.getSecretManagerClient();
  const secretsToGet = secrets
    .filter((secret) => secret.secretName[stageName] !== null)
    .map((secret) => {
      const secretName = secret.secretName[stageName] as string;
      return {
        key: secret.key,
        name: secretName,
      };
    });
  const uniqueSecretsNames = [
    ...new Set(
      secretsToGet.map((secret) => secret.name),
    ),
  ]; // Remove duplicates so that we don't make requests for the same secret more than once
  Logger.info(`Retrieving secrets:\n${uniqueSecretsNames.join("\n")}`);
  const secretResults = await Promise.allSettled(uniqueSecretsNames.map(async (secretName) => {
    const secretValue = await secretManager.projects.locations.secrets.versions.access({
      name: `projects/appscript-296515/secrets/${secretName}/versions/latest`,
    });
    const secretContent = secretValue.data.payload?.data;
    const secretString = atob(secretContent as string).replaceAll("\n", "");
    return secretString as string;
  }));
  const secretsFetchErrors = [];
  for (const [index, secretValue] of secretResults.entries()) {
    if (secretValue.status === "rejected") {
      const errorMessage = secretValue.reason.errors?.map((error: { message: string }) => error.message).join("\n");
      secretsFetchErrors.push(errorMessage);
      continue;
    }
    const secretName = uniqueSecretsNames[index];
    const secretsToSet = secretsToGet.filter((secret) => secret.name === secretName);
    secretsToSet.forEach((secret) => {
      env[secret.key] = secretValue.value as string;
    });
  }
  if (secretsFetchErrors.length > 0) {
    throw new Error(`Failed to fetch secrets:\n- ${secretsFetchErrors.join("\n- ")}`);
  }
  return env;
};
